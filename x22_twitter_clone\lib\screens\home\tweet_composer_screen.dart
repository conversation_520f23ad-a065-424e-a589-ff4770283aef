import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../providers/tweets_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/api_service.dart';

class TweetComposerScreen extends StatefulWidget {
  final int? replyToTweetId;

  const TweetComposerScreen({super.key, this.replyToTweetId});

  @override
  State<TweetComposerScreen> createState() => _TweetComposerScreenState();
}

class _TweetComposerScreenState extends State<TweetComposerScreen> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ImagePicker _imagePicker = ImagePicker();
  static const int maxCharacters = 280;

  String? _uploadedImageUrl;
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _postTweet() async {
    if (_textController.text.trim().isEmpty) {
      return;
    }

    final tweetsProvider = Provider.of<TweetsProvider>(context, listen: false);

    final success = await tweetsProvider.createTweet(
      content: _textController.text.trim(),
      imageUrl: _uploadedImageUrl,
      replyToTweetId: widget.replyToTweetId,
    );

    if (success && mounted) {
      Navigator.of(context).pop(true);
    }
  }

  void _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _isUploadingImage = true;
        });

        final imageUrl = await ApiService.uploadImage(File(image.path));

        setState(() {
          _uploadedImageUrl = imageUrl;
          _isUploadingImage = false;
        });

        if (imageUrl == null && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to upload image. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isUploadingImage = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage() {
    setState(() {
      _uploadedImageUrl = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.replyToTweetId != null ? 'Reply' : 'Compose Tweet'),
        actions: [
          Consumer<TweetsProvider>(
            builder: (context, tweetsProvider, child) {
              final canPost =
                  _textController.text.trim().isNotEmpty &&
                  _textController.text.length <= maxCharacters;

              return TextButton(
                onPressed:
                    (canPost && !tweetsProvider.isLoading) ? _postTweet : null,
                child:
                    tweetsProvider.isLoading
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.blue,
                            ),
                          ),
                        )
                        : Text(
                          widget.replyToTweetId != null ? 'Reply' : 'Post',
                          style: TextStyle(
                            color: canPost ? Colors.blue : Colors.grey,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // User info and text input
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // User avatar and info
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Avatar
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          return CircleAvatar(
                            radius: 24,
                            backgroundColor: Colors.blue,
                            backgroundImage:
                                authProvider.user?.profileImageUrl != null
                                    ? NetworkImage(
                                      authProvider.user!.profileImageUrl!,
                                    )
                                    : null,
                            child:
                                authProvider.user?.profileImageUrl == null
                                    ? Text(
                                      authProvider.user?.displayName
                                              .substring(0, 1)
                                              .toUpperCase() ??
                                          'U',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )
                                    : null,
                          );
                        },
                      ),
                      const SizedBox(width: 12),

                      // Text input
                      Expanded(
                        child: TextField(
                          controller: _textController,
                          focusNode: _focusNode,
                          maxLines: null,
                          maxLength: maxCharacters,
                          decoration: InputDecoration(
                            hintText:
                                widget.replyToTweetId != null
                                    ? 'Post your reply'
                                    : "What's happening?",
                            border: InputBorder.none,
                            counterText: '',
                          ),
                          style: const TextStyle(fontSize: 18),
                          onChanged: (text) {
                            setState(() {});
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Bottom section with character count and actions
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey.shade200)),
            ),
            child: Row(
              children: [
                // Media buttons (placeholder for future implementation)
                IconButton(
                  onPressed: () {
                    // TODO: Implement image picker
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Image upload coming soon!'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.image, color: Colors.blue),
                ),
                IconButton(
                  onPressed: () {
                    // TODO: Implement GIF picker
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('GIF support coming soon!')),
                    );
                  },
                  icon: const Icon(Icons.gif, color: Colors.blue),
                ),

                const Spacer(),

                // Character count
                AnimatedBuilder(
                  animation: _textController,
                  builder: (context, child) {
                    final currentLength = _textController.text.length;
                    final remaining = maxCharacters - currentLength;
                    final isOverLimit = remaining < 0;

                    return Row(
                      children: [
                        // Circular progress indicator for character count
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            value: currentLength / maxCharacters,
                            strokeWidth: 2,
                            backgroundColor: Colors.grey.shade300,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isOverLimit
                                  ? Colors.red
                                  : remaining <= 20
                                  ? Colors.orange
                                  : Colors.blue,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),

                        // Character count text
                        if (remaining <= 20)
                          Text(
                            remaining.toString(),
                            style: TextStyle(
                              color: isOverLimit ? Colors.red : Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),

          // Error message
          Consumer<TweetsProvider>(
            builder: (context, tweetsProvider, child) {
              if (tweetsProvider.errorMessage != null) {
                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  color: Colors.red.shade50,
                  child: Text(
                    tweetsProvider.errorMessage!,
                    style: TextStyle(color: Colors.red.shade700),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }
}
